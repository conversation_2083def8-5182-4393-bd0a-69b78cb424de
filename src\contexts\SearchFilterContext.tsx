import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Salon, Service, Staff } from '@/types';
import { mockServices, mockStaff } from '@/lib/mockData';

// Search and Filter Types
export interface SearchFilters {
  location: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  priceRange: [number, number];
  rating: number;
  services: string[];
  sortBy: 'price' | 'rating' | 'distance' | 'name';
  sortOrder: 'asc' | 'desc';
}

export interface SearchFilterContextType {
  // Search state
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  
  // Filter state
  filters: SearchFilters;
  setFilters: (filters: Partial<SearchFilters>) => void;
  resetFilters: () => void;
  
  // Salon data
  allSalons: Salon[];
  filteredSalons: Salon[];
  setAllSalons: (salons: Salon[]) => void;
  
  // View state
  viewMode: 'grid' | 'map';
  setViewMode: (mode: 'grid' | 'map') => void;
  
  // Filter modal state
  isFilterModalOpen: boolean;
  setIsFilterModalOpen: (open: boolean) => void;
  
  // Loading state
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const defaultFilters: SearchFilters = {
  location: '',
  checkIn: '',
  checkOut: '',
  guests: 1,
  priceRange: [0, 200],
  rating: 0,
  services: [],
  sortBy: 'rating',
  sortOrder: 'desc',
};

const SearchFilterContext = createContext<SearchFilterContextType | undefined>(undefined);

export const useSearchFilter = () => {
  const context = useContext(SearchFilterContext);
  if (!context) {
    throw new Error('useSearchFilter must be used within a SearchFilterProvider');
  }
  return context;
};

interface SearchFilterProviderProps {
  children: ReactNode;
}

export const SearchFilterProvider: React.FC<SearchFilterProviderProps> = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFiltersState] = useState<SearchFilters>(defaultFilters);
  const [allSalons, setAllSalons] = useState<Salon[]>([]);
  const [filteredSalons, setFilteredSalons] = useState<Salon[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const setFilters = (newFilters: Partial<SearchFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFiltersState(defaultFilters);
    setSearchQuery('');
  };

  // Filter and search logic
  useEffect(() => {
    let filtered = [...allSalons];

    // Apply search query - now includes services and staff
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(salon => {
        // Search in salon basic info
        const salonMatch =
          salon.name.toLowerCase().includes(query) ||
          salon.location.toLowerCase().includes(query) ||
          salon.description.toLowerCase().includes(query) ||
          salon.address.toLowerCase().includes(query);

        // Search in salon services
        const salonServices = mockServices.filter(service => service.salonId === salon.id);
        const serviceMatch = salonServices.some(service =>
          service.name.toLowerCase().includes(query) ||
          service.description?.toLowerCase().includes(query) ||
          service.category.toLowerCase().includes(query)
        );

        // Search in salon staff
        const salonStaff = mockStaff.filter(staff => staff.salonId === salon.id);
        const staffMatch = salonStaff.some(staff =>
          staff.name.toLowerCase().includes(query) ||
          staff.specialty.toLowerCase().includes(query) ||
          staff.bio?.toLowerCase().includes(query)
        );

        return salonMatch || serviceMatch || staffMatch;
      });
    }

    // Apply location filter
    if (filters.location.trim()) {
      const location = filters.location.toLowerCase();
      filtered = filtered.filter(salon => 
        salon.location.toLowerCase().includes(location) ||
        salon.address.toLowerCase().includes(location)
      );
    }

    // Apply price range filter (assuming we add price to salon or calculate from services)
    // For now, we'll use a mock price based on rating
    filtered = filtered.filter(salon => {
      const estimatedPrice = salon.rating * 20; // Mock price calculation
      return estimatedPrice >= filters.priceRange[0] && estimatedPrice <= filters.priceRange[1];
    });

    // Apply rating filter
    if (filters.rating > 0) {
      filtered = filtered.filter(salon => salon.rating >= filters.rating);
    }

    // Apply services filter
    if (filters.services.length > 0) {
      filtered = filtered.filter(salon => {
        const salonServices = mockServices.filter(service => service.salonId === salon.id);
        return filters.services.some(filterService =>
          salonServices.some(service =>
            service.category.toLowerCase() === filterService.toLowerCase() ||
            service.name.toLowerCase().includes(filterService.toLowerCase())
          )
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: number | string;
      let bValue: number | string;

      switch (filters.sortBy) {
        case 'price':
          aValue = a.rating * 20; // Mock price
          bValue = b.rating * 20;
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'distance':
          // Mock distance calculation based on location
          aValue = a.distance.length;
          bValue = b.distance.length;
          break;
        default:
          aValue = a.rating;
          bValue = b.rating;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return filters.sortOrder === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

    setFilteredSalons(filtered);
  }, [allSalons, searchQuery, filters]);

  const value: SearchFilterContextType = {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    resetFilters,
    allSalons,
    filteredSalons,
    setAllSalons,
    viewMode,
    setViewMode,
    isFilterModalOpen,
    setIsFilterModalOpen,
    isLoading,
    setIsLoading,
  };

  return (
    <SearchFilterContext.Provider value={value}>
      {children}
    </SearchFilterContext.Provider>
  );
};
