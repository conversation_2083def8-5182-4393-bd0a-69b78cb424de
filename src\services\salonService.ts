import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Salon, SalonForm } from '@/types';

export class SalonService {
  private static readonly COLLECTION = 'salons';

  // Create a new salon
  static async createSalon(salonData: SalonForm & { ownerId: string }): Promise<string> {
    try {
      const salon: Omit<Salon, 'id'> = {
        ...salonData,
        rating: 0,
        reviews: 0,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<Salon>(this.COLLECTION, salon);
    } catch (error) {
      console.error('Error creating salon:', error);
      throw error;
    }
  }

  // Get salon by ID
  static async getSalonById(id: string): Promise<Salon | null> {
    try {
      return await FirestoreService.getById<Salon>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting salon:', error);
      throw error;
    }
  }

  // Get all active salons
  static async getActiveSalons(): Promise<Salon[]> {
    try {
      return await FirestoreService.getWithQuery<Salon>(this.COLLECTION, [
        where('isActive', '==', true),
        orderBy('rating', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting active salons:', error);
      throw error;
    }
  }

  // Get salons by location
  static async getSalonsByLocation(location: string): Promise<Salon[]> {
    try {
      return await FirestoreService.getWithQuery<Salon>(this.COLLECTION, [
        where('location', '==', location),
        where('isActive', '==', true),
        orderBy('rating', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting salons by location:', error);
      throw error;
    }
  }

  // Get salons by owner
  static async getSalonsByOwner(ownerId: string): Promise<Salon[]> {
    try {
      return await FirestoreService.getWithQuery<Salon>(this.COLLECTION, [
        where('ownerId', '==', ownerId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting salons by owner:', error);
      throw error;
    }
  }

  // Update salon
  static async updateSalon(id: string, updates: Partial<Salon>): Promise<void> {
    try {
      await FirestoreService.update<Salon>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating salon:', error);
      throw error;
    }
  }

  // Delete salon (soft delete by setting isActive to false)
  static async deleteSalon(id: string): Promise<void> {
    try {
      await FirestoreService.update<Salon>(this.COLLECTION, id, { 
        isActive: false 
      });
    } catch (error) {
      console.error('Error deleting salon:', error);
      throw error;
    }
  }

  // Search salons by name
  static async searchSalons(searchTerm: string): Promise<Salon[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation - consider using Algolia or similar for better search
      const allSalons = await this.getActiveSalons();
      return allSalons.filter(salon => 
        salon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        salon.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        salon.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching salons:', error);
      throw error;
    }
  }

  // Get all salons (admin only)
  static async getAllSalons(): Promise<Salon[]> {
    try {
      return await FirestoreService.getAll<Salon>(this.COLLECTION);
    } catch (error) {
      console.error('Error getting all salons:', error);
      throw error;
    }
  }

  // Update salon rating
  static async updateSalonRating(salonId: string, newRating: number, reviewCount: number): Promise<void> {
    try {
      await FirestoreService.update<Salon>(this.COLLECTION, salonId, {
        rating: newRating,
        reviews: reviewCount
      });
    } catch (error) {
      console.error('Error updating salon rating:', error);
      throw error;
    }
  }

  // Listen to salon changes
  static onSalonChange(salonId: string, callback: (salon: Salon | null) => void): () => void {
    return FirestoreService.onDocumentChange<Salon>(this.COLLECTION, salonId, callback);
  }

  // Listen to salons collection changes
  static onSalonsChange(callback: (salons: Salon[]) => void): () => void {
    return FirestoreService.onCollectionChange<Salon>(this.COLLECTION, callback, [
      where('isActive', '==', true),
      orderBy('rating', 'desc')
    ]);
  }
}
