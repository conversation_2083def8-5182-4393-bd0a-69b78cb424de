import { 
  Salon, 
  Service, 
  Staff, 
  Booking, 
  SalonRegistrationRequest, 
  AnalyticsData,
  User,
  Admin,
  SalonOwner
} from '@/types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'admin-1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  } as Admin,
  {
    id: 'owner-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'salon_owner',
    salonId: '1',
    isApproved: true,
    requestedAt: '2024-01-01T00:00:00Z',
    approvedAt: '2024-01-02T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  } as SalonOwner,
];

// Mock Salons
export const mockSalons: Salon[] = [
  {
    id: '1',
    name: 'The Hair Lounge',
    description: 'Premier salon in the heart of San Francisco offering expert styling and treatments.',
    location: 'San Francisco',
    address: '123 Market Street, San Francisco, CA 94102',
    distance: 'Downtown',
    rating: 4.8,
    reviews: 120,
    images: ['/salon-1.jpg', '/salon-2.jpg'],
    ownerId: 'owner-1',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Bella Beauty Studio',
    description: 'Full-service beauty salon and spa with modern amenities.',
    location: 'San Francisco',
    address: '456 Union Square, San Francisco, CA 94108',
    distance: 'Union Square',
    rating: 4.6,
    reviews: 89,
    images: ['/salon-3.jpg', '/salon-4.jpg'],
    ownerId: 'owner-2',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: '3',
    name: 'Style & Grace',
    description: 'Modern hair styling and color specialists with experienced staff.',
    location: 'San Francisco',
    address: '789 Castro Street, San Francisco, CA 94114',
    distance: 'Castro District',
    rating: 4.9,
    reviews: 156,
    images: ['/salon-5.jpg', '/salon-6.jpg'],
    ownerId: 'owner-3',
    isActive: false,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
];

// Mock Services - Comprehensive data for all salons
export const mockServices: Service[] = [
  // Chic Hair Studio (salon 1)
  {
    id: 'service-1',
    salonId: '1',
    name: 'Haircut & Style',
    description: 'Professional haircut with styling',
    price: 85,
    duration: 60,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'service-2',
    salonId: '1',
    name: 'Hair Coloring',
    description: 'Full hair coloring service',
    price: 150,
    duration: 120,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'service-3',
    salonId: '1',
    name: 'Highlights',
    description: 'Professional hair highlights',
    price: 120,
    duration: 90,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  // Urban Beauty Lounge (salon 2)
  {
    id: 'service-4',
    salonId: '2',
    name: 'Facial Treatment',
    description: 'Relaxing facial treatment',
    price: 120,
    duration: 90,
    category: 'Skincare',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: 'service-5',
    salonId: '2',
    name: 'Manicure',
    description: 'Professional manicure service',
    price: 45,
    duration: 45,
    category: 'Nails',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: 'service-6',
    salonId: '2',
    name: 'Pedicure',
    description: 'Relaxing pedicure service',
    price: 55,
    duration: 60,
    category: 'Nails',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: 'service-7',
    salonId: '2',
    name: 'Eyebrow Threading',
    description: 'Precise eyebrow shaping',
    price: 25,
    duration: 30,
    category: 'Skincare',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  // The Style Loft (salon 3)
  {
    id: 'service-8',
    salonId: '3',
    name: 'Balayage',
    description: 'Hand-painted hair coloring technique',
    price: 180,
    duration: 150,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  {
    id: 'service-9',
    salonId: '3',
    name: 'Keratin Treatment',
    description: 'Hair smoothing treatment',
    price: 200,
    duration: 120,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  {
    id: 'service-10',
    salonId: '3',
    name: 'Blowout',
    description: 'Professional hair styling',
    price: 65,
    duration: 45,
    category: 'Hair',
    isActive: true,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  // Glamour Zone (salon 4)
  {
    id: 'service-11',
    salonId: '4',
    name: 'Deep Cleansing Facial',
    description: 'Intensive facial treatment',
    price: 140,
    duration: 75,
    category: 'Skincare',
    isActive: true,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
  {
    id: 'service-12',
    salonId: '4',
    name: 'Anti-Aging Facial',
    description: 'Rejuvenating facial treatment',
    price: 160,
    duration: 90,
    category: 'Skincare',
    isActive: true,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
  {
    id: 'service-13',
    salonId: '4',
    name: 'Gel Manicure',
    description: 'Long-lasting gel nail polish',
    price: 65,
    duration: 60,
    category: 'Nails',
    isActive: true,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
];

// Mock Staff - Comprehensive data for all salons
export const mockStaff: Staff[] = [
  // Chic Hair Studio (salon 1)
  {
    id: 'staff-1',
    salonId: '1',
    name: 'Emily Carter',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Hair cutting and styling specialist',
    bio: 'Emily has over 8 years of experience in hair styling and cutting.',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'staff-2',
    salonId: '1',
    name: 'Sophia Bennett',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Color specialist and highlights',
    bio: 'Sophia specializes in hair coloring and chemical treatments.',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'staff-3',
    salonId: '1',
    name: 'Marcus Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Senior stylist and hair treatments',
    bio: 'Marcus is a senior stylist with expertise in modern cuts and treatments.',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  // Urban Beauty Lounge (salon 2)
  {
    id: 'staff-4',
    salonId: '2',
    name: 'Olivia Hayes',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Skincare and facial treatments',
    bio: 'Olivia is a licensed esthetician with expertise in skincare.',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: 'staff-5',
    salonId: '2',
    name: 'Isabella Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Nail art and manicure specialist',
    bio: 'Isabella creates beautiful nail art and specializes in nail care.',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: 'staff-6',
    salonId: '2',
    name: 'Zoe Chen',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Eyebrow threading and waxing',
    bio: 'Zoe specializes in eyebrow shaping and facial hair removal.',
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  // The Style Loft (salon 3)
  {
    id: 'staff-7',
    salonId: '3',
    name: 'Alexander Kim',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Balayage and color correction',
    bio: 'Alexander is an expert in advanced coloring techniques.',
    isActive: true,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  {
    id: 'staff-8',
    salonId: '3',
    name: 'Maya Patel',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Keratin treatments and hair smoothing',
    bio: 'Maya specializes in hair treatments and smoothing services.',
    isActive: true,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  // Glamour Zone (salon 4)
  {
    id: 'staff-9',
    salonId: '4',
    name: 'Victoria Thompson',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Anti-aging treatments and luxury facials',
    bio: 'Victoria provides premium skincare and anti-aging treatments.',
    isActive: true,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
  {
    id: 'staff-10',
    salonId: '4',
    name: 'James Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    specialty: 'Gel manicures and nail design',
    bio: 'James creates stunning nail designs and specializes in gel applications.',
    isActive: true,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
];

// Mock Bookings
export const mockBookings: Booking[] = [
  {
    id: 'booking-1',
    salonId: '1',
    customerName: 'Sarah Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    serviceId: 'service-1',
    staffId: 'staff-1',
    date: '2024-01-15',
    time: '10:00 AM',
    status: 'confirmed',
    totalAmount: 85,
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: 'booking-2',
    salonId: '2',
    customerName: 'Mike Chen',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    serviceId: 'service-3',
    staffId: 'staff-3',
    date: '2024-01-15',
    time: '2:30 PM',
    status: 'pending',
    totalAmount: 120,
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
  },
  {
    id: 'booking-3',
    salonId: '1',
    customerName: 'Emma Davis',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    serviceId: 'service-4',
    staffId: 'staff-2',
    date: '2024-01-16',
    time: '4:00 PM',
    status: 'completed',
    totalAmount: 45,
    createdAt: '2024-01-11T00:00:00Z',
    updatedAt: '2024-01-16T00:00:00Z',
  },
];

// Mock Salon Registration Requests
export const mockSalonRequests: SalonRegistrationRequest[] = [
  {
    id: 'request-1',
    ownerName: 'David Kim',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+****************',
    salonName: 'Modern Hair Studio',
    salonDescription: 'Contemporary hair styling studio specializing in modern cuts and color techniques.',
    salonAddress: '456 Style Street, San Francisco, CA 94108',
    businessLicense: 'BL-2024-002',
    status: 'pending',
    submittedAt: '2024-01-12T00:00:00Z',
  },
  {
    id: 'request-2',
    ownerName: 'Lisa Thompson',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+****************',
    salonName: 'Glamour Point',
    salonDescription: 'Boutique salon focusing on personalized beauty services and client satisfaction.',
    salonAddress: '789 Beauty Blvd, San Francisco, CA 94114',
    businessLicense: 'BL-2024-003',
    status: 'approved',
    submittedAt: '2024-01-08T00:00:00Z',
    reviewedAt: '2024-01-09T00:00:00Z',
    reviewedBy: 'admin-1',
  },
];

// Mock Analytics Data
export const mockAnalyticsData: AnalyticsData = {
  totalRevenue: 45250,
  totalBookings: 342,
  totalSalons: 24,
  totalCustomers: 189,
  revenueByMonth: [
    { month: 'Jan', revenue: 3200 },
    { month: 'Feb', revenue: 3800 },
    { month: 'Mar', revenue: 4100 },
    { month: 'Apr', revenue: 3900 },
    { month: 'May', revenue: 4500 },
    { month: 'Jun', revenue: 4800 },
  ],
  popularServices: [
    { serviceName: 'Haircut & Style', bookings: 89 },
    { serviceName: 'Facial Treatment', bookings: 67 },
    { serviceName: 'Manicure', bookings: 54 },
    { serviceName: 'Hair Coloring', bookings: 43 },
    { serviceName: 'Massage', bookings: 38 },
  ],
  peakHours: [
    { hour: '9 AM', bookings: 12 },
    { hour: '10 AM', bookings: 18 },
    { hour: '11 AM', bookings: 25 },
    { hour: '12 PM', bookings: 22 },
    { hour: '1 PM', bookings: 15 },
    { hour: '2 PM', bookings: 28 },
    { hour: '3 PM', bookings: 32 },
    { hour: '4 PM', bookings: 29 },
    { hour: '5 PM', bookings: 24 },
    { hour: '6 PM', bookings: 16 },
  ],
  salonPerformance: [
    { salonName: 'The Hair Lounge', revenue: 12500, bookings: 89 },
    { salonName: 'Bella Beauty', revenue: 10200, bookings: 76 },
    { salonName: 'Style & Grace', revenue: 9800, bookings: 68 },
    { salonName: 'Glamour Point', revenue: 8900, bookings: 62 },
    { salonName: 'Modern Studio', revenue: 7600, bookings: 54 },
  ],
};
