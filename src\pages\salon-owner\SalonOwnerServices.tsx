import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Scissors, DollarSign, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { Service } from '@/types';

const SalonOwnerServices = () => {
  // Mock services data - in real app, this would come from API
  const [services, setServices] = useState<Service[]>([
    {
      id: 'service-1',
      salonId: '1',
      name: 'Haircut & Style',
      description: 'Professional haircut with styling',
      price: 85,
      duration: 60,
      category: 'Hair',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: 'service-2',
      salonId: '1',
      name: 'Hair Coloring',
      description: 'Full hair coloring service',
      price: 150,
      duration: 120,
      category: 'Hair',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: 'service-3',
      salonId: '1',
      name: 'Facial Treatment',
      description: 'Relaxing facial with premium products',
      price: 120,
      duration: 90,
      category: 'Skincare',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ]);

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    duration: '',
    category: '',
  });

  const categories = ['Hair', 'Skincare', 'Nails', 'Massage', 'Waxing', 'Other'];

  const handleAddService = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      duration: '',
      category: '',
    });
    setIsAddDialogOpen(true);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      description: service.description || '',
      price: service.price.toString(),
      duration: service.duration.toString(),
      category: service.category,
    });
    setIsEditDialogOpen(true);
  };

  const handleSubmitForm = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.price || !formData.duration || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    const serviceData = {
      name: formData.name,
      description: formData.description,
      price: parseFloat(formData.price),
      duration: parseInt(formData.duration),
      category: formData.category,
    };

    if (editingService) {
      // Update existing service
      setServices(prev =>
        prev.map(service =>
          service.id === editingService.id
            ? { ...service, ...serviceData, updatedAt: new Date().toISOString() }
            : service
        )
      );
      toast.success(`Service "${formData.name}" has been updated`);
      setIsEditDialogOpen(false);
      setEditingService(null);
    } else {
      // Add new service
      const newService: Service = {
        id: `service-${Date.now()}`,
        salonId: '1', // In real app, get from auth context
        ...serviceData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setServices(prev => [...prev, newService]);
      toast.success(`Service "${formData.name}" has been added`);
      setIsAddDialogOpen(false);
    }
    
    setFormData({
      name: '',
      description: '',
      price: '',
      duration: '',
      category: '',
    });
  };

  const handleToggleActive = (serviceId: string) => {
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? { ...service, isActive: !service.isActive, updatedAt: new Date().toISOString() }
          : service
      )
    );
    toast.success('Service status updated');
  };

  const handleDeleteService = (service: Service) => {
    setServices(prev => prev.filter(s => s.id !== service.id));
    toast.success(`Service "${service.name}" has been deleted`);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  const ServiceForm = () => (
    <form onSubmit={handleSubmitForm} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Service Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Haircut & Style"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select
            value={formData.category}
            onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Describe your service..."
          rows={3}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="price">Price ($) *</Label>
          <Input
            id="price"
            type="number"
            min="0"
            step="0.01"
            value={formData.price}
            onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
            placeholder="0.00"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="duration">Duration (minutes) *</Label>
          <Input
            id="duration"
            type="number"
            min="15"
            step="15"
            value={formData.duration}
            onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
            placeholder="60"
            required
          />
        </div>
      </div>
      
      <DialogFooter>
        <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          {editingService ? 'Update Service' : 'Add Service'}
        </Button>
      </DialogFooter>
    </form>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Services & Pricing</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage your salon's services and pricing
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddService} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
              <Plus className="w-4 h-4 mr-2" />
              Add Service
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Service</DialogTitle>
              <DialogDescription>
                Create a new service for your salon
              </DialogDescription>
            </DialogHeader>
            <ServiceForm />
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Services
            </CardTitle>
            <Scissors className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{services.length}</div>
            <p className="text-xs text-green-600 mt-1">
              {services.filter(s => s.isActive).length} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Price
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              ${Math.round(services.reduce((sum, s) => sum + s.price, 0) / services.length)}
            </div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Across all services
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Duration
            </CardTitle>
            <Clock className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              {Math.round(services.reduce((sum, s) => sum + s.duration, 0) / services.length)}m
            </div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Average service time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Services Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="w-5 h-5 text-glamspot-primary" />
            Your Services
          </CardTitle>
          <CardDescription>
            Manage your salon's service offerings and pricing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {services.map((service) => (
                <TableRow key={service.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium text-glamspot-neutral-900">{service.name}</p>
                      {service.description && (
                        <p className="text-sm text-glamspot-neutral-500 mt-1">{service.description}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{service.category}</Badge>
                  </TableCell>
                  <TableCell className="font-medium">${service.price}</TableCell>
                  <TableCell>{formatDuration(service.duration)}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={service.isActive}
                        onCheckedChange={() => handleToggleActive(service.id)}
                      />
                      <span className={`text-sm ${service.isActive ? 'text-green-600' : 'text-red-600'}`}>
                        {service.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditService(service)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteService(service)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Service Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Service</DialogTitle>
            <DialogDescription>
              Update your service details
            </DialogDescription>
          </DialogHeader>
          <ServiceForm />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonOwnerServices;
