import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Service, ServiceForm } from '@/types';

export class ServiceService {
  private static readonly COLLECTION = 'services';

  // Create a new service
  static async createService(serviceData: ServiceForm & { salonId: string }): Promise<string> {
    try {
      const service: Omit<Service, 'id'> = {
        ...serviceData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<Service>(this.COLLECTION, service);
    } catch (error) {
      console.error('Error creating service:', error);
      throw error;
    }
  }

  // Get service by ID
  static async getServiceById(id: string): Promise<Service | null> {
    try {
      return await FirestoreService.getById<Service>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting service:', error);
      throw error;
    }
  }

  // Get services by salon
  static async getServicesBySalon(salonId: string): Promise<Service[]> {
    try {
      return await FirestoreService.getWithQuery<Service>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('isActive', '==', true),
        orderBy('name', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting services by salon:', error);
      throw error;
    }
  }

  // Get services by category
  static async getServicesByCategory(category: string): Promise<Service[]> {
    try {
      return await FirestoreService.getWithQuery<Service>(this.COLLECTION, [
        where('category', '==', category),
        where('isActive', '==', true),
        orderBy('price', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting services by category:', error);
      throw error;
    }
  }

  // Get all active services
  static async getActiveServices(): Promise<Service[]> {
    try {
      return await FirestoreService.getWithQuery<Service>(this.COLLECTION, [
        where('isActive', '==', true),
        orderBy('name', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting active services:', error);
      throw error;
    }
  }

  // Update service
  static async updateService(id: string, updates: Partial<Service>): Promise<void> {
    try {
      await FirestoreService.update<Service>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating service:', error);
      throw error;
    }
  }

  // Delete service (soft delete)
  static async deleteService(id: string): Promise<void> {
    try {
      await FirestoreService.update<Service>(this.COLLECTION, id, { 
        isActive: false 
      });
    } catch (error) {
      console.error('Error deleting service:', error);
      throw error;
    }
  }

  // Get all services (admin only)
  static async getAllServices(): Promise<Service[]> {
    try {
      return await FirestoreService.getAll<Service>(this.COLLECTION);
    } catch (error) {
      console.error('Error getting all services:', error);
      throw error;
    }
  }

  // Search services by name
  static async searchServices(searchTerm: string, salonId?: string): Promise<Service[]> {
    try {
      let services: Service[];
      
      if (salonId) {
        services = await this.getServicesBySalon(salonId);
      } else {
        services = await this.getActiveServices();
      }
      
      return services.filter(service => 
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        service.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching services:', error);
      throw error;
    }
  }

  // Get services by price range
  static async getServicesByPriceRange(
    minPrice: number, 
    maxPrice: number, 
    salonId?: string
  ): Promise<Service[]> {
    try {
      let services: Service[];
      
      if (salonId) {
        services = await this.getServicesBySalon(salonId);
      } else {
        services = await this.getActiveServices();
      }
      
      return services.filter(service => 
        service.price >= minPrice && service.price <= maxPrice
      );
    } catch (error) {
      console.error('Error getting services by price range:', error);
      throw error;
    }
  }

  // Get unique categories
  static async getServiceCategories(): Promise<string[]> {
    try {
      const services = await this.getActiveServices();
      const categories = [...new Set(services.map(service => service.category))];
      return categories.sort();
    } catch (error) {
      console.error('Error getting service categories:', error);
      throw error;
    }
  }

  // Listen to services changes for a salon
  static onSalonServicesChange(salonId: string, callback: (services: Service[]) => void): () => void {
    return FirestoreService.onCollectionChange<Service>(this.COLLECTION, callback, [
      where('salonId', '==', salonId),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    ]);
  }

  // Batch update services
  static async batchUpdateServices(updates: Array<{ id: string; data: Partial<Service> }>): Promise<void> {
    try {
      const operations = updates.map(update => ({
        type: 'update' as const,
        collection: this.COLLECTION,
        id: update.id,
        data: update.data
      }));
      
      await FirestoreService.batchWrite(operations);
    } catch (error) {
      console.error('Error batch updating services:', error);
      throw error;
    }
  }
}
